// Use type safe message keys with `next-intl`
type Messages = typeof import('../locales/en.json');

// eslint-disable-next-line
declare interface IntlMessages extends Messages { }

// Define server action response type
export type ServerActionResponse<T> = {
  data?: T;
  error?: string;
  success: boolean;
};

export type IFileResponse = {
  mimeType: string;
  originalname: string;
  key: string;
  filename: string;
  url: string;
  _id: string;
};

export type EditorContentChanged = {
  html: string;
  markdown: string;
};

export type nameStateAgentCopilotkit =
  | 'client_summarize_state'
  | 'client_assessment_state'
  | 'brief_analysis_state'
  | 'content_editing_state';

export type stateRouteAgent<T> = {
  agent_name: string;
} & {
  [key in nameStateAgentCopilotkit]?: T;
};
