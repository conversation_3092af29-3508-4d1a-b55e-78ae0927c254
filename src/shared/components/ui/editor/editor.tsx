'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';
import 'react-quill-new/dist/quill.snow.css';
import { htmlToMarkdown, markdownToHtml } from './parser';
import type { EditorContentChanged } from '@/shared/types/global';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

const TOOLBAR_OPTIONS = [
  [{ header: [1, 2, 3, false] }],
  ['bold', 'italic', 'underline', 'strike', 'blockquote', 'link'],
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ indent: '-1' }, { indent: '+1' }],
  ['clean'],
];

export default function Editor({ value: initialValue = '', onChange }: { value?: string; onChange?: (value: EditorContentChanged) => void }) {
  const [value, setValue] = useState<string>(() => markdownToHtml(initialValue || ''));

  const handleChange = (content: string) => {
    setValue(content);
    if (onChange) {
      onChange({
        html: content,
        markdown: htmlToMarkdown(content),
      });
    }
  };

  return (
    <ReactQuill
      theme="snow"
      placeholder="Start writing..."
      modules={{
        toolbar: {
          container: TOOLBAR_OPTIONS,
        },

      }}
      value={value}
      onChange={handleChange}
    />
  );
}
