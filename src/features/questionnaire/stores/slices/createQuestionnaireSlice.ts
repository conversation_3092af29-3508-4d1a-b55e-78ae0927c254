import { MOCK_QUESTIONNAIRE } from '../../constants/mock-questionnaire';
import type { QuestionnaireResponse } from '../../types/questionnaire';
import type { QuestionnaireSlice, QuestionnaireStoreStateCreator } from '../types';

export const createQuestionnaireSlice: QuestionnaireStoreStateCreator<QuestionnaireSlice> = (set, _get) => ({

  id: '',

  questionnaire: MOCK_QUESTIONNAIRE as QuestionnaireResponse,

  actions: {
    initializeQuestionnaire: (uploaded) => {
      set({ questionnaire: uploaded });
    },
  },
});
