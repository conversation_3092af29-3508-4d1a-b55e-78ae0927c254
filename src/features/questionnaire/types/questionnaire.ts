export type QuestionnaireResponse = {
  id: string;
  introduction: string | null;
  isActive: boolean;
  sections: SectionsQuestionnaire[];
  startDate: string;
  endDate: string;
  updatedAt: string;
  createdAt: string;
};

export type SectionsQuestionnaire = {
  title: string;
  description: string;
  questions: QuestionsInSection[];
};

export type QuestionsInSection = {
  type: QuestionType;
  title: string;
  answer: null | string;
  options: OptionsSelectionInQuestion[];
  subQuestions?: QuestionsInSection[];
};

export type OptionsSelectionInQuestion = {
  title: string;
  isCustom: boolean;
  isSelected: boolean;
  value: null | string;
};

export enum QuestionType {
  TEXT = 'text',
  MULTIPLE_TEXT = 'multiple_text',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  CATEGORY = 'category',
  EVALUATION = 'evaluation',
}
