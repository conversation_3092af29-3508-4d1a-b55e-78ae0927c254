'use client';

import React from 'react';
import { DocumentIcon } from '@/shared/icons';
import QuestionnaireSection from './QuestionnaireSection';

const QuestionnaireWrapper: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#EEF2FF] py-8 px-4">
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
            <DocumentIcon className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">
            Market Sizing Questionnaire
          </h1>
          <p className="text-gray-600 mb-1">
            Help us understand your vision needs and preferences
          </p>
          <p className="text-sm text-blue-600">
            *This survey takes approximately 10-15 minutes to complete.
          </p>
        </div>

        <div className="bg-white shadow-sm rounded-2xl p-4 mb-4">
          <QuestionnaireSection type="markdown" />
        </div>

        <div className="bg-white shadow-sm rounded-2xl p-4">
          <QuestionnaireSection type="info" />
        </div>
      </div>
    </div>
  );
};

export default QuestionnaireWrapper;
