'use client';

import React, { useEffect } from 'react';
import { DocumentIcon } from '@/shared/icons';
import QuestionnaireSection from './QuestionnaireSection';
import ProjectCardSkeleton from '@/features/project-management/components/project-list/ProjectCardSkeleton';
import { useQuestionnaireData } from '../../stores';
import QuestionnaireMarkdown from './QuestionnaireMarkdown';

const QuestionnaireWrapper: React.FC = () => {
  const questionnaire = useQuestionnaireData();

  useEffect(() => {
    if (questionnaire) {
      console.log(questionnaire);
    }
  }, [questionnaire]);

  return (
    <div className="min-h-screen bg-[#EEF2FF] py-8 px-4">
      <div className="max-w-5xl mx-auto">
        {
          !questionnaire
            ? (
                <>
                  <h3>Waiting...</h3>
                  <ProjectCardSkeleton />
                </>
              )
            : (
                <>
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                      <DocumentIcon className="w-8 h-8 text-blue-600" />
                    </div>
                    <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                      Market Sizing Questionnaire
                    </h1>
                    <p className="text-gray-600 mb-1">
                      Help us understand your vision needs and preferences
                    </p>
                    <p className="text-sm text-blue-600">
                      *This survey takes approximately 10-15 minutes to complete.
                    </p>
                  </div>

                  {questionnaire.introduction && (
                    <div className="bg-white shadow-sm rounded-2xl p-4 mb-4">
                      <QuestionnaireMarkdown markdown={questionnaire.introduction} />
                    </div>
                  )}

                  {
                    questionnaire.sections.map((section, index) => (
                      <div key={index} className="bg-white shadow-sm rounded-2xl p-4">
                        <QuestionnaireSection section={section} />
                      </div>
                    ))
                  }
                </>
              )
        }
      </div>
    </div>
  );
};

export default QuestionnaireWrapper;
