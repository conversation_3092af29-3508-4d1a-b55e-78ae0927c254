'use client';

import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { MOCK_HEADER_MARKDOWN } from '../../constants/mock-header-markdown';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Radio from '@/shared/components/form/input/Radio';
import Checkbox from '@/shared/components/form/input/Checkbox';

// Form validation schema
const questionnaireSchema = z.object({
  fullName: z.string().optional(),
  tel: z.string().optional(),
  currentAddress: z.string().optional(),
  interviewSite: z.string().optional(),
  sex: z.string().optional(),
  age: z.string().optional(),
  eyeLaserBenefits: z.array(z.string()).optional(),
  otherBenefit: z.string().optional(),
  medicalFacilities: z.record(z.string()).optional(),
  hospitalCriteria: z.array(z.string()).optional(),
  otherCriteria: z.string().optional(),
});

type QuestionnaireFormData = z.infer<typeof questionnaireSchema>;

type QuestionnaireFormDataExtend = QuestionnaireFormData & {
  [key: string]: any;
};

const QuestionnaireSection: React.FC<{ type: string }> = ({ type }) => {
  // Form state management
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<QuestionnaireFormDataExtend>({
    resolver: zodResolver(questionnaireSchema),
    defaultValues: {
      fullName: '',
      tel: '',
      currentAddress: '',
      interviewSite: '',
      sex: '',
      age: '',
      eyeLaserBenefits: [],
      otherBenefit: '',
      medicalFacilities: {},
      hospitalCriteria: [],
      otherCriteria: '',
    },
  });

  const onSubmit = (data: QuestionnaireFormData) => {
    console.log('Form data:', data);
  };

  return (
    <>
      {
        type === 'markdown' && (
          <MarkdownRenderer content={MOCK_HEADER_MARKDOWN} />
        )
      }

      {
        type === 'info' && (
          <div>
            {/* header */}
            <div className=" p-1.5 px-4 border-l-4 border-sky-600">
              <h4 className="text-sky-600">Screening Information</h4>
              <div className="">Please provide us your basic information for survey screening process.</div>
            </div>
            {/* content */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4">
              {/* Full name */}
              <div>
                <Label htmlFor="fullName">Full name:</Label>
                <Controller
                  name="fullName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="fullName"
                      type="text"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.fullName}
                      hint={errors.fullName?.message}
                    />
                  )}
                />
              </div>

              {/* Tel */}
              <div>
                <Label htmlFor="tel">Tel:</Label>
                <Controller
                  name="tel"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="tel"
                      type="tel"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.tel}
                      hint={errors.tel?.message}
                    />
                  )}
                />
              </div>

              {/* Current address */}
              <div>
                <Label htmlFor="currentAddress">Current address:</Label>
                <Controller
                  name="currentAddress"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="currentAddress"
                      type="text"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.currentAddress}
                      hint={errors.currentAddress?.message}
                    />
                  )}
                />
              </div>

              {/* Interview/survey site */}
              <div>
                <Label>Interview/survey site:</Label>
                <Controller
                  name="interviewSite"
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-wrap gap-4 mt-2">
                      {['Hà Nội', 'TP.Vĩnh', 'Đà Nẵng', 'Quy Nhon', 'Cần Thơ', 'TP. Hồ Chí Minh'].map(site => (
                        <Radio
                          key={site}
                          id={`site-${site}`}
                          name="interviewSite"
                          value={site}
                          checked={field.value === site}
                          onChange={field.onChange}
                          label={site}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Sex */}
              <div>
                <Label>Sex:</Label>
                <Controller
                  name="sex"
                  control={control}
                  render={({ field }) => (
                    <div className="flex gap-4 mt-2">
                      {['Male', 'Female'].map(gender => (
                        <Radio
                          key={gender}
                          id={`sex-${gender}`}
                          name="sex"
                          value={gender}
                          checked={field.value === gender}
                          onChange={field.onChange}
                          label={gender}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Age */}
              <div>
                <Label>Age:</Label>
                <Controller
                  name="age"
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-wrap gap-4 mt-2">
                      {['Under 18', '18 - 24', '25 - 34', '35 - 42', 'Above 42'].map(ageRange => (
                        <Radio
                          key={ageRange}
                          id={`age-${ageRange}`}
                          name="age"
                          value={ageRange}
                          checked={field.value === ageRange}
                          onChange={field.onChange}
                          label={ageRange}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Eye Laser Surgery Benefits */}
              <div>
                <Label>What do you think are the benefits of Eye Laser Surgery? (Select all that apply)</Label>
                <Controller
                  name="eyeLaserBenefits"
                  control={control}
                  render={({ field }) => {
                    const benefits = [
                      'Being able to see clearly without glasses or contact lenses',
                      'Very convenient for daily activities and sports (e.g., driving, watching 3D movies, swimming, playing soccer, etc.)',
                      'Saving money on glasses and lenses',
                      'Permanently cured eye sight conditions',
                      'No more discomfortness when wearing glasses',
                      'Feel more confident about appearance',
                      'Saving time spent on glasses and contact lenses cares',
                      'More confidence and convenience at workplace',
                    ];

                    const handleCheckboxChange = (benefit: string, checked: boolean) => {
                      const currentValues = field.value || [];
                      if (checked) {
                        field.onChange([...currentValues, benefit]);
                      } else {
                        field.onChange(currentValues.filter((item: string) => item !== benefit));
                      }
                    };

                    return (
                      <div className="space-y-3 mt-2">
                        {benefits.map(benefit => (
                          <Checkbox
                            key={benefit}
                            id={`benefit-${benefit}`}
                            label={benefit}
                            checked={(field.value || []).includes(benefit)}
                            onChange={checked => handleCheckboxChange(benefit, checked)}
                          />
                        ))}

                        {/* Other option with text input */}
                        <div className="flex items-start gap-3">
                          <Controller
                            name="eyeLaserBenefits"
                            control={control}
                            render={({ field: benefitsField }) => (
                              <Checkbox
                                id="benefit-other"
                                classNameLabel="shrink-0"
                                label="Other (please specify):"
                                checked={(benefitsField.value || []).includes('Other')}
                                onChange={(checked) => {
                                  const currentValues = benefitsField.value || [];
                                  if (checked) {
                                    benefitsField.onChange([...currentValues, 'Other']);
                                  } else {
                                    benefitsField.onChange(currentValues.filter((item: string) => item !== 'Other'));
                                  }
                                }}
                              />
                            )}
                          />
                          <Controller
                            name="otherBenefit"
                            control={control}
                            render={({ field: otherField }) => (
                              <Input
                                id="otherBenefit"
                                type="text"
                                value={otherField.value}
                                onChange={otherField.onChange}
                                classNameParent="w-full"
                                className="flex-1 p-0 h-[unset] border-0 border-b rounded-none w-full focus:border-0 hover:border-0 focus:box-shadow-none"
                                error={!!errors.otherBenefit}
                                hint={errors.otherBenefit?.message}
                              />
                            )}
                          />
                        </div>
                      </div>
                    );
                  }}
                />
              </div>

              {/* Medical Facilities Satisfaction */}
              <div>
                <Label>Please select all medical facilities that provide refractive surgery services (that you are aware of) from the following brands</Label>
                <Controller
                  name="medicalFacilities"
                  control={control}
                  render={({ field }) => {
                    const facilities = [
                      'Ho Chi Minh City Eye Hospital (Bệnh viện Mắt TP.HCM)',
                      'Saigon Eye Hospital',
                      'Tam Anh Hospital',
                    ];

                    const satisfactionLevels = [
                      'Very satisfied',
                      'Satisfied',
                      'Dissatisfied',
                      'Very dissatisfied',
                      'Good',
                      'Bad',
                      'Excellent',
                    ];

                    const handleRatingChange = (facility: string, rating: string) => {
                      const currentValues = field.value || {};
                      field.onChange({
                        ...currentValues,
                        [facility]: rating,
                      });
                    };

                    return (
                      <div className="mt-4">
                        {/* Table with Sticky First Column */}
                        <div className="overflow-x-auto border border-gray-300 rounded-lg">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="sticky left-0 z-10 bg-gray-50 border-r border-gray-300 p-3 text-left font-medium text-gray-700 min-w-[300px] shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]">
                                  Medical Facility
                                </th>
                                {satisfactionLevels.map(level => (
                                  <th key={level} className="border-l border-gray-300 p-3 text-center font-medium text-gray-700 min-w-[120px] whitespace-nowrap">
                                    {level}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {facilities.map(facility => (
                                <tr key={facility} className="hover:bg-gray-50 border-t border-gray-300">
                                  <td className="sticky left-0 z-10 bg-white hover:bg-gray-50 border-r border-gray-300 p-3 text-sm text-gray-700 min-w-[300px] shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]">
                                    {facility}
                                  </td>
                                  {satisfactionLevels.map(level => (
                                    <td key={level} className="border-l border-gray-300 p-3 text-center min-w-[120px]">
                                      <div className="flex justify-center">
                                        <Radio
                                          id={`${facility}-${level}`}
                                          name={`facility-${facility}`}
                                          value={level}
                                          checked={(field.value || {})[facility] === level}
                                          onChange={() => handleRatingChange(facility, level)}
                                          label=""
                                          className="justify-center"
                                        />
                                      </div>
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    );
                  }}
                />
              </div>

              {/* Hospital Selection Criteria */}
              <div>
                <Label>Which of the below do you consider when choosing a hospital/medical facility for refractive surgery? (Select up to 5)</Label>
                <Controller
                  name="hospitalCriteria"
                  control={control}
                  render={({ field }) => {
                    // Define all criteria categories and their options
                    const criteriaCategories = [
                      {
                        title: 'Facilities',
                        options: [
                          'Convenient location (Near home)',
                          'Clean and modern facilities',
                          'Modern, advanced equipment',
                        ],
                      },
                      {
                        title: 'Cost & Promotions',
                        options: [
                          'Lower surgery cost',
                          'Flexible payment policy (Installments)',
                          'Promotional offers (free consultation, discounted surgery packages, etc.)',
                        ],
                      },
                      {
                        title: 'Reputation',
                        options: [
                          'Highly rated by relatives, friends, colleagues',
                          'Good reviews from celebrities, Facebook groups, Google, etc.',
                          'Long-established hospital',
                          'Central / state-owned hospital',
                          'International brand (Advanced, luxurious, foreign doctors)',
                        ],
                      },
                      {
                        title: 'Products & Services',
                        options: [
                          'Wide range of options (methods and prices)',
                          'Dedicated, transparent, non-coercive consultation',
                          'Presence of leading eye specialists',
                          'Recommended by reputable doctors (Head of department, Deputy head of department)',
                        ],
                      },
                    ];

                    const handleCriteriaChange = (criteria: string, checked: boolean) => {
                      const currentValues = field.value || [];
                      if (checked && currentValues.length < 5) {
                        field.onChange([...currentValues, criteria]);
                      } else if (!checked) {
                        field.onChange(currentValues.filter((item: string) => item !== criteria));
                      }
                    };

                    return (
                      <div className="mt-4 space-y-6">
                        {/* Map through criteria categories */}
                        {criteriaCategories.map((category, index) => (
                          <div key={category.title}>
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-3">{category.title}</h4>
                              <div className="space-y-2">
                                {category.options.map(criteria => (
                                  <Checkbox
                                    key={criteria}
                                    id={`criteria-${criteria}`}
                                    label={criteria}
                                    checked={(field.value || []).includes(criteria)}
                                    onChange={checked => handleCriteriaChange(criteria, checked)}
                                    disabled={(field.value || []).length >= 5 && !(field.value || []).includes(criteria)}
                                  />
                                ))}
                              </div>
                            </div>
                            {/* Other option with text input */}
                            <div className="flex items-start gap-3 mt-3">
                              <Controller
                                name={`otherCriteria-${index}}`}
                                control={control}
                                render={({ field: criteriaField }) => (
                                  <Checkbox
                                    id="criteria-other"
                                    label="Other (please specify):"
                                    classNameLabel="shrink-0"
                                    checked={(criteriaField.value || []).includes('Other')}
                                    onChange={(checked) => {
                                      const currentValues = criteriaField.value || [];
                                      if (checked && currentValues.length < 5) {
                                        criteriaField.onChange([...currentValues, 'Other']);
                                      } else if (!checked) {
                                        criteriaField.onChange(currentValues.filter((item: string) => item !== 'Other'));
                                      }
                                    }}
                                    disabled={(criteriaField.value || []).length >= 5 && !(criteriaField.value || []).includes('Other')}
                                  />
                                )}
                              />
                              <Controller
                                name={`otherCriteria-${index}`}
                                control={control}
                                render={({ field: otherField }) => (
                                  <Input
                                    id="otherCriteria"
                                    type="text"
                                    value={otherField.value}
                                    onChange={otherField.onChange}
                                    classNameParent="w-full"
                                    className="
                                    flex-1
                                    p-0
                                    h-[unset]
                                    w-full
                                    border-0
                                    border-b
                                    border-solid
                                    rounded-none
                                    focus:shadow-[none]
                                    focus:border-black
                                    hover:border-b-[1px]
                                    hover:border-black
                                    "
                                    error={!!errors.otherCriteria}
                                    hint={errors.otherCriteria?.message}
                                  />
                                )}
                              />
                            </div>
                          </div>
                        ))}

                      </div>
                    );
                  }}
                />

              </div>
            </form>
          </div>
        )
      }
    </>
  );
};

export default QuestionnaireSection;
