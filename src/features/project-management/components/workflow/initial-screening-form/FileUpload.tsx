'use client';

import type { IFileResponse } from '@/shared/types/global';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import { http } from '@/core/http/http';
import { Env } from '@/core/config/Env';

type FileUploadProps = {
  onFilesChange: (files: IFileResponse[]) => void;
  initialFile?: IFileResponse[];
};

const FileUpload: React.FC<FileUploadProps> = ({ onFilesChange, initialFile }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [fileUrls, setFileUrls] = useState<IFileResponse[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  const setInitialFile = () => {
    if (initialFile?.length) {
      const saveFile = () => {
        setFileUrls(initialFile);
      };

      saveFile();
    }
  };

  useEffect(() => {
    setInitialFile();
  }, [initialFile]);

  const handleUploadFile = async (files: File[]) => {
    if (!files.length) {
      return Promise.all([]);
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const uploadPromises = files.map((file, index) => {
        const formData = new FormData();
        formData.append('file', file);

        return http.post<IFileResponse>({
          url: '/files/upload',
          data: formData,
          options: {
            headers: { 'Content-Type': 'multipart/form-data' },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setUploadProgress((prev) => {
                  const totalProgress = (prev * index + percentCompleted) / (index + 1);
                  return Math.round(totalProgress);
                });
              }
            },
          },
        });
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      toast.error('Failed to upload files');
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      try {
        const newFiles = [...files, ...acceptedFiles];
        setFiles(newFiles);

        const fileResponses = await handleUploadFile(acceptedFiles);
        const file = fileResponses.map(response => response.data);
        const newFileResponses = [...fileUrls, ...file].filter(f => !!f);
        console.log('File upload responses:', fileResponses);
        setFileUrls(newFileResponses);
        // Pass both files and URLs to parent component
        onFilesChange(newFileResponses);
      } catch (error) {
        console.error('Error in file upload process:', error);
      }
    },
    [files, fileUrls, onFilesChange],
  );

  const removeFile = (index: number) => {
    const newFiles = [...files];
    const newFileUrls = [...fileUrls];

    newFiles.splice(index, 1);
    newFileUrls.splice(index, 1);

    setFiles(newFiles);
    setFileUrls(newFileUrls);
    onFilesChange(newFileUrls);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'application/pdf': [],
      'application/msword': [],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [],
      'application/vnd.ms-excel': [],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
      'application/vnd.ms-powerpoint': [],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': [],
    },
    disabled: isUploading,
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors ${
          isDragActive
            ? 'border-primary bg-primary/5'
            : 'border-border hover:border-primary/70'
        } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center text-center">
          {isUploading
            ? (
                <div className="flex flex-col items-center w-full">
                  <svg className="animate-spin h-10 w-10 text-primary mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p className="text-sm text-foreground mb-2">
                    Uploading files...
                    {uploadProgress}
                    %
                  </p>
                  <div className="w-full bg-muted rounded-full h-2.5">
                    <div
                      className="bg-primary h-2.5 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    >
                    </div>
                  </div>
                </div>
              )
            : (
                <>
                  <svg
                    className="w-10 h-10 mb-3 text-muted-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    >
                    </path>
                  </svg>
                  <p className="mb-2 text-sm text-foreground">
                    <span className="font-medium">Click to upload</span>
                    {' '}
                    or drag and drop
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Images, PDFs, Word, Excel, PowerPoint files
                  </p>
                </>
              )}
        </div>
      </div>

      {fileUrls.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-foreground mb-2">
            Uploaded Files (
            {fileUrls.length}
            )
          </h4>
          <ul className="space-y-2">
            {fileUrls.map((file, index) => (
              <li
                key={`${file.originalname}-${index}`}
                className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-2 text-muted-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    >
                    </path>
                  </svg>
                  <span className="text-sm text-foreground truncate max-w-xs">
                    {file.originalname}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {file && (
                    <a
                      href={`${Env.NEXT_PUBLIC_API_SERVER}/public/${file.key}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                      </svg>
                    </a>
                  )}
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="text-destructive hover:text-destructive/80"
                    disabled={isUploading}
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      >
                      </path>
                    </svg>
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
