'use client';

import { useEffect, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useCurrentStep, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';

const ReviewInputWrapper: React.FC = () => {
  const [markdown, setMarkdown] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const { completeStep } = useWorkflowActions();

  const { data } = useGetInfoDetail<any>(currentStepId ?? '');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const updateMarkdownToState = (data: string) => {
    setMarkdown(data);
    setIsLoading(false);
    return true;
  };

  useEffect(() => {
    console.log(data);
    if (data?.stepInfo.length && data?.stepInfo[0]?.infos?.length && data?.stepInfo[0]?.infos[0]?.value) {
      updateMarkdownToState(data?.stepInfo[0]?.infos[0]?.value);
    }
  }, [data]);

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);

    completeStep(currentStepId);

    // setIsEditMode(false);
    // if (editorRef.current) {
    //   editorRef.current.handleSubmitForm();
    // }
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6">

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText="Start Generate"
              showPrevious={true}
              prevButtonText="Back"
              panelClass=""
            />
          </div>

          <div className="">

            <MarkdownRenderer content={markdown} />
          </div>
        </div>
      );
};

export default ReviewInputWrapper;
