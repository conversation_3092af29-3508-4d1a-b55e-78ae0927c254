'use client';

import { But<PERSON> } from '@/shared/components/ui/button';
import { MOCK_MARKDOWN_DATA } from '@/features/project-management/constants/mock-markdown';
import { ArrowDownTrayIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import { useEffect, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useCurrentStep } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import Editor from '@/shared/components/ui/editor/editor';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import type { EditorContentChanged } from '@/shared/types/global';

const BriefAnalysisWrapper: React.FC = () => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  // const editorRef = useRef<any>(null);

  const { state } = useCoAgent<BriefAnalysisFlow>({
    name: 'brief_analysis_flow',
    initialState: {},
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  useEffect(() => {
    updateMarkdownToState(MOCK_MARKDOWN_DATA);

    // if (state.brief_analysis_output && state.brief_analysis_process && state.brief_analysis_process === 'done') {
    //   updateMarkdownToState(state.brief_analysis_output);
    // } else if (state.brief_analysis_process && state.brief_analysis_process === 'done') {
    //   updateMarkdownToState(MOCK_MARKDOWN_DATA);
    // }

    // if (state.brief_analysis_output && state.brief_analysis_process && state.brief_analysis_process === 'done') {
    //   updateMarkdownToState(state.brief_analysis_output);
    // } else if (state.brief_analysis_process && state.brief_analysis_process === 'done') {
    //   updateMarkdownToState(MOCK_MARKDOWN_DATA);
    // }
  }, [state]);

  const toggleEditMode = () => {
    // if (editorRef.current) {
    //   const markdown = editorRef.current.handleSubmitForm();
    //   setMarkdown(markdown);
    // }

    setIsEditMode(prev => !prev);
  };

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    // if (editorRef.current) {
    //   editorRef.current.handleSubmitForm();
    // }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);
  };

  const discardChange = () => {
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={discardChange}
                    >
                      Discard Change
                    </Button>

                    <Button
                      type="button"
                      onClick={confirmChange}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Confirm
                    </Button>
                  </>
                )
              : (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={toggleEditMode}
                    >
                      <FileEditIcon className="h-5 w-5 " />
                      Edit
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button>
                    <Button
                      type="button"
                      onClick={handleSubmit}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  </>
                )}

          </div>

          <div className="mt-6">
            {
              isEditMode
                ? <Editor onChange={handleChangeEditor} value={form} />
                : <MarkdownRenderer content={markdown} />
            }
          </div>
        </div>
      );
};

export default BriefAnalysisWrapper;
