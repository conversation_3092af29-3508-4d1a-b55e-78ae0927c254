'use client';

import Label from '@/shared/components/form/Label';
import FileUpload from '../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse } from '@/shared/types/global';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useClientFileUploaded, useCurrentStep, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { useGetInfoDetail, useProjectDetail } from '@/features/project-management/hooks';
import { useParams } from 'next/navigation';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { Role, TextMessage } from 'node_modules/@copilotkit/runtime-client-gql/dist/client/types';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import type { fileUploadResponse, stepInfoFile } from '@/features/project-management/types/project';
import { Env } from '@/core/config/Env';

const ClientUploadForm: React.FC = () => {
  const [_files, setFiles] = useState<IFileResponse[]>([]);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  // Service options data
  const serviceOptions = [
    { id: 'corporate', label: 'Corporate' },
    { id: 'crisis-management', label: 'Crisis Management' },
    { id: 'event', label: 'Event' },
    { id: 'gr-advocacy', label: 'GR-Advocacy' },
    { id: 'imc', label: 'IMC' },
    { id: 'market-research', label: 'Market Research' },
    { id: 'media-relation-pr', label: 'Media Relation - PR' },
    { id: 'mibrand-branding', label: 'Mibrand Branding' },
    { id: 'product-launch', label: 'Product Launch' },
    { id: 'social-digital-corporate', label: 'Social & Digital Corporate' },
    { id: 'social-media-digital-product', label: 'Social Media & Digital Product' },
    { id: 'tvc-video-production', label: 'TVC/Video Production' },
  ];

  // Custom Hook

  const currentStep = useCurrentStep();

  const currentStepId = currentStep?.id;

  const clientFileUploaded = useClientFileUploaded();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { setClientFileUploaded } = useWorkflowActions();

  const { data: fileUpload } = useGetInfoDetail<fileUploadResponse, any>(currentStep?.id ?? '');

  //  Set Agent

  const { setState: setCoAgentsState } = useCoAgent<BriefAnalysisFlow>({
    name: 'brief_analysis_flow',
    initialState: {},
  });

  const { appendMessage } = useCopilotChat();

  // project detail

  const params = useParams<{ id: string }>();

  const { data: project } = useProjectDetail(params.id);

  const saveInitialFile = (file: IFileResponse[]) => {
    const setInitialFileStore = () => {
      setInitialFile(file);
    };

    setInitialFileStore();
  };

  const updateInitialFile = () => {
    const setFile = () => {
      const files = ((fileUpload?.stepInfo[0]?.infos ?? []).map(
        (info: stepInfoFile) => ({
          mimeType: info.type,
          originalname: info.name,
          key: info.file,
          filename: info.name,
          url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${info.file}`,
          _id: info.id,
        }),
      ));
      saveInitialFile(files);
      setClientFileUploaded(files);
    };

    setFile();
  };

  useEffect(() => {
    saveInitialFile(clientFileUploaded);
    if (fileUpload && fileUpload.stepInfo.length) {
      updateInitialFile();
    }
  }, [fileUpload]);

  const setProjectIdInAgent = React.useCallback(() => {
    if (project) {
      setCoAgentsState(coAgentState => ({
        ...coAgentState,
        project_id: project?.id,
      }));
    }
  }, [project]);

  useEffect(() => {
    setProjectIdInAgent();
  }, [setProjectIdInAgent]);

  const {
    completeStep,
  } = useWorkflowActions();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const handleOptionToggle = (optionId: string) => {
    setSelectedOptions(prev =>
      prev.includes(optionId)
        ? prev.filter(id => id !== optionId)
        : [...prev, optionId],
    );
  };

  const handleSendMessage = () => {
    setCoAgentsState(prevState => ({
      ...prevState,
      client_brief_url: getFile(_files),
    }));

    appendMessage(
      new TextMessage({
        content: 'Trigger Brief Analysis',
        role: Role.Developer,
      }),
    );
  };

  const onSubmit = async () => {
    if (!currentStepId) {
      return;
    }
    const payload = {
      stepInfos: [
        {
          order: 0,
          infos: _files.map(file => ({
            file: file.key,
            name: file.originalname,
            type: file.mimeType,
            id: file._id,
          })),
        },
      ],
    };

    await updateQuestionAnswer(
      payload,
      currentStepId,
    );

    handleSendMessage();

    completeStep(currentStepId);
  };

  return (
    <div className="relative">
      <div className="space-y-6 p-4 md:p-6">
        {/* Service Options Grid */}
        <div className="mb-6">
          <div className="grid grid-cols-3 gap-4">
            {serviceOptions.map(option => (
              <button
                key={option.id}
                type="button"
                onClick={() => handleOptionToggle(option.id)}
                className={`
                  px-4 py-3 text-sm font-medium rounded-lg border transition-colors
                  ${selectedOptions.includes(option.id)
                ? 'bg-blue-50 border-blue-200 text-blue-700'
                : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
              }
                `}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        <Label htmlFor="files" className="mb-1.5 block text-primary">
          Attached Files
        </Label>
        <FileUpload initialFile={initialFile} onFilesChange={handleFilesChange} />
      </div>

      <WorkflowNavigation
        onComplete={onSubmit}
        nextButtonText="Generate"
        showPrevious={false}
      />
    </div>
  );
};

export default ClientUploadForm;
