import { useWorkflowStoreSelector } from '../store';

/**
 * Hook for accessing the workflow tasks array
 *
 * @returns Array of workflow tasks
 */
export function useWorkflowTasks() {
  return useWorkflowStoreSelector(state => state.workflow);
}

/**
 * Hook for accessing the current task
 *
 * @returns Current task or null
 */
export function useCurrentTask() {
  return useWorkflowStoreSelector(state => state.currentTask);
}

/**
 * Hook for accessing the current step
 *
 * @returns Current step or null
 */
export function useCurrentStep() {
  return useWorkflowStoreSelector(state => state.currentStep);
}

/**
 * Hook for accessing workflow loading state
 *
 * @returns Boolean indicating if workflow is loading
 */
export function useWorkflowLoading() {
  return useWorkflowStoreSelector(state => state.isLoading);
}

/**
 * Hook for accessing workflow error state
 *
 * @returns Error string or null
 */
export function useWorkflowError() {
  return useWorkflowStoreSelector(state => state.error);
}

/**
 * Hook for accessing current step info IDs
 *
 * @returns Current step info IDs string
 */
export function useCurrentStepInfoIds() {
  return useWorkflowStoreSelector(state => state.currentStepInfoIds);
}

/**
 * Hook for accessing project name
 *
 * @returns Project name string
 */
export function useProjectName() {
  return useWorkflowStoreSelector(state => state.projectName);
}

/**
 * Hook for accessing actions related to workflow slice
 */
export function useWorkflowActions() {
  return useWorkflowStoreSelector(state => state.actions);
}

export function useClientFileUploaded() {
  return useWorkflowStoreSelector(state => state.clientFileUploaded);
}
