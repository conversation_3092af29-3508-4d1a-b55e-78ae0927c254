'use client';

import { useQuery } from '@tanstack/react-query';
import { getTeamMembers } from '@/features/team-members/services/team-member.service';
import { useCallback, useMemo, useState } from 'react';

/**
 * Hook for fetching team members for project assignment
 *
 * This hook provides a way to fetch team members for the project form.
 * It transforms the data into the format needed for the SearchableMultiSelect component.
 *
 * @returns Team members data for dropdown with search functionality
 */
export function useTeamMembersForProject() {
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch team members with search query
  const { data, isLoading, error } = useQuery({
    queryKey: ['team-members-for-project', searchQuery],
    queryFn: () => getTeamMembers({
      searchQuery,
      limit: 50,
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Transform team members data for the SearchableMultiSelect component
  const teamMemberOptions = useMemo(() => {
    if (!data?.data?.items) {
      return [];
    }

    return data.data.items.map(user => ({
      value: user.id,
      text: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
      selected: false,
    }));
  }, [data]);

  // Handle search input changes
  const handleSearch = useCallback((query: string) => {
    // Pass the query as is, even if it contains only spaces
    setSearchQuery(query);
    console.log('Searching for:', query); // For debugging
  }, []);

  return {
    teamMemberOptions,
    isLoading,
    error,
    searchQuery,
    handleSearch,
  };
}
